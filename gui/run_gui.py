#!/usr/bin/env python3
"""
启动SAM2 GUI的简单脚本
"""

import os
import sys

# 确保在正确的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
os.chdir(parent_dir)

# 添加路径
sys.path.insert(0, parent_dir)

# 导入并运行GUI
from gui.sam2_gui import create_interface

if __name__ == "__main__":
    print("🌱 启动SAM2植物分割工具...")
    print(f"📁 工作目录: {os.getcwd()}")
    
    # 检查必要文件
    checkpoint_path = "./checkpoints/sam2.1_hiera_large.pt"
    config_path = "./sam2/configs/sam2.1/sam2.1_hiera_l.yaml"
    
    if not os.path.exists(checkpoint_path):
        print(f"❌ 模型文件不存在: {checkpoint_path}")
        print("请确保已下载模型文件到checkpoints目录")
        sys.exit(1)
    
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        sys.exit(1)
    
    print("✅ 文件检查通过")
    
    # 创建并启动界面
    demo = create_interface()
    print("🚀 启动Gradio界面...")
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        debug=True
    )
