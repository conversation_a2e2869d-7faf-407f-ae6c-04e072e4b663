#!/usr/bin/env python3
"""
SAM2 GUI - A Gradio interface for Segment Anything 2
用于植物分割的SAM2图形界面
"""

import os
import sys
import numpy as np
import torch
import gradio as gr
from PIL import Image
import cv2
import glob
from pathlib import Path

# 添加SAM2路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sam2.build_sam import build_sam2
from sam2.sam2_image_predictor import SAM2ImagePredictor

# 尝试导入gradio_image_annotator
try:
    import gradio_image_annotator as gia
    HAS_ANNOTATOR = True
except ImportError:
    print("⚠️ gradio_image_annotator未安装，将使用基础标注功能")
    HAS_ANNOTATOR = False

class SAM2GUI:
    def __init__(self):
        self.predictor = None
        self.current_image = None
        self.current_image_path = None
        self.image_list = []
        self.current_index = 0
        self.device = "cuda" if torch.cuda.is_available() else "cpu"

        # 标注数据
        self.positive_points = []  # 正点
        self.negative_points = []  # 负点
        self.boxes = []           # 框选
        self.current_annotation_mode = "positive"  # positive, negative, box

        # 初始化SAM2模型
        self.init_sam2_model()
    
    def init_sam2_model(self):
        """初始化SAM2模型"""
        try:
            # 模型配置
            checkpoint = "./checkpoints/sam2.1_hiera_large.pt"
            model_cfg = "configs/sam2.1/sam2.1_hiera_l.yaml"
            
            # 检查模型文件是否存在
            if not os.path.exists(checkpoint):
                raise FileNotFoundError(f"模型文件不存在: {checkpoint}")
            
            # 构建模型
            sam2_model = build_sam2(model_cfg, checkpoint, device=self.device)
            self.predictor = SAM2ImagePredictor(sam2_model)
            
            print(f"SAM2模型已加载到设备: {self.device}")
            return "✅ SAM2模型加载成功"
            
        except Exception as e:
            error_msg = f"❌ 模型加载失败: {str(e)}"
            print(error_msg)
            return error_msg
    
    def clear_annotations(self):
        """清除所有标注"""
        self.positive_points = []
        self.negative_points = []
        self.boxes = []

    def get_image_choices(self):
        """获取图像选择列表"""
        if not self.image_list:
            return []

        choices = []
        for i, image_path in enumerate(self.image_list):
            filename = os.path.basename(image_path)
            choices.append(f"{i+1:03d} - {filename}")

        return choices

    def load_images_from_directory(self, directory_path):
        """从目录加载所有图像"""
        if not directory_path or not os.path.exists(directory_path):
            return "❌ 目录路径无效", None, "没有图像", 0, 0, []

        # 支持的图像格式
        image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.tif']

        # 获取所有图像文件
        image_files = []
        for ext in image_extensions:
            image_files.extend(glob.glob(os.path.join(directory_path, ext)))
            image_files.extend(glob.glob(os.path.join(directory_path, ext.upper())))

        # 排序文件列表
        image_files.sort()

        if not image_files:
            return "❌ 目录中没有找到图像文件", None, "没有图像", 0, 0, []

        self.image_list = image_files
        self.current_index = 0
        self.clear_annotations()  # 清除之前的标注

        # 加载第一张图像
        first_image, info = self.load_image_by_index(0)

        # 获取图像选择列表
        choices = self.get_image_choices()

        status = f"✅ 成功加载 {len(image_files)} 张图像"
        return status, first_image, info, 1, len(image_files), gr.Dropdown(choices=choices, value=choices[0] if choices else None)
    
    def load_image_by_index(self, index):
        """根据索引加载图像"""
        if not self.image_list or index < 0 or index >= len(self.image_list):
            return None, "无效的图像索引"

        try:
            image_path = self.image_list[index]
            image = Image.open(image_path).convert("RGB")

            # 保存当前图像信息
            self.current_image = np.array(image)
            self.current_image_path = image_path
            self.current_index = index
            self.clear_annotations()  # 切换图像时清除标注

            # 设置图像到预测器
            if self.predictor:
                with torch.inference_mode(), torch.autocast(self.device, dtype=torch.bfloat16):
                    self.predictor.set_image(self.current_image)

            # 图像信息
            filename = os.path.basename(image_path)
            info = f"图像: {filename} ({index + 1}/{len(self.image_list)}) - 尺寸: {image.size}"

            return image, info

        except Exception as e:
            return None, f"❌ 加载图像失败: {str(e)}"

    def select_image_from_dropdown(self, choice):
        """从下拉列表选择图像"""
        if not choice or not self.image_list:
            return None, "没有选择图像", 0, 0, self.get_annotation_info()

        try:
            # 解析选择的索引 (格式: "001 - filename.jpg")
            index = int(choice.split(" - ")[0]) - 1
            if 0 <= index < len(self.image_list):
                image, info = self.load_image_by_index(index)
                return image, info, index + 1, len(self.image_list), self.get_annotation_info()
        except Exception as e:
            print(f"选择图像失败: {e}")

        return None, "选择失败", 0, 0, self.get_annotation_info()
    
    def navigate_image(self, direction):
        """导航图像 (上一张/下一张)"""
        if not self.image_list:
            return None, "没有加载图像", 1, len(self.image_list) if self.image_list else 0, None, self.get_annotation_info()

        if direction == "prev":
            new_index = max(0, self.current_index - 1)
        else:  # next
            new_index = min(len(self.image_list) - 1, self.current_index + 1)

        image, info = self.load_image_by_index(new_index)
        choices = self.get_image_choices()
        current_choice = choices[new_index] if choices else None

        return image, info, new_index + 1, len(self.image_list), gr.Dropdown(value=current_choice), self.get_annotation_info()
    
    def process_annotations(self, annotated_data):
        """处理标注数据"""
        if self.current_image is None:
            return self.current_image, "❌ 请先加载图像", self.get_annotation_info()

        try:
            # 清除之前的标注
            self.clear_annotations()

            if annotated_data and 'annotations' in annotated_data:
                annotations = annotated_data['annotations']

                for ann in annotations:
                    if ann['type'] == 'point':
                        # 点标注
                        x, y = ann['coordinates']
                        if ann.get('label', 'positive') == 'positive':
                            self.positive_points.append([x, y])
                        else:
                            self.negative_points.append([x, y])
                    elif ann['type'] == 'rectangle':
                        # 矩形标注
                        coords = ann['coordinates']
                        # 转换为 [x1, y1, x2, y2] 格式
                        x1, y1 = coords[0]
                        x2, y2 = coords[2]  # 对角点
                        self.boxes.append([min(x1, x2), min(y1, y2), max(x1, x2), max(y1, y2)])

            status = f"✅ 标注已更新"
            return annotated_data, status, self.get_annotation_info()

        except Exception as e:
            return annotated_data, f"❌ 处理标注失败: {str(e)}", self.get_annotation_info()

    def add_annotation_legacy(self, evt: gr.SelectData):
        """传统标注方法（备用）"""
        if self.current_image is None:
            return self.current_image, "❌ 请先加载图像", self.get_annotation_info()

        try:
            x, y = evt.index[0], evt.index[1]

            if self.current_annotation_mode == "positive":
                self.positive_points.append([x, y])
                status = f"✅ 添加正点: ({x}, {y})"
            elif self.current_annotation_mode == "negative":
                self.negative_points.append([x, y])
                status = f"✅ 添加负点: ({x}, {y})"
            elif self.current_annotation_mode == "box":
                # 框选模式：需要两个点
                if len(self.boxes) == 0 or len(self.boxes[-1]) == 4:
                    # 开始新的框选
                    self.boxes.append([x, y])
                    status = f"✅ 框选起点: ({x}, {y})"
                else:
                    # 完成框选
                    start_x, start_y = self.boxes[-1]
                    self.boxes[-1] = [min(start_x, x), min(start_y, y), max(start_x, x), max(start_y, y)]
                    status = f"✅ 完成框选: ({min(start_x, x)}, {min(start_y, y)}) 到 ({max(start_x, x)}, {max(start_y, y)})"

            # 可视化当前标注
            annotated_image = self.visualize_annotations()

            return annotated_image, status, self.get_annotation_info()

        except Exception as e:
            return self.current_image, f"❌ 添加标注失败: {str(e)}", self.get_annotation_info()

    def get_annotation_info(self):
        """获取当前标注信息"""
        info = f"正点: {len(self.positive_points)}, 负点: {len(self.negative_points)}, 框选: {len([b for b in self.boxes if len(b) == 4])}"
        return info

    def clear_all_annotations(self):
        """清除所有标注"""
        self.clear_annotations()
        if self.current_image is not None:
            # 返回清空的标注数据
            if HAS_ANNOTATOR:
                empty_data = {"image": Image.fromarray(self.current_image), "annotations": []}
                return empty_data, "✅ 已清除所有标注", self.get_annotation_info()
            else:
                # 重新显示原图
                image = Image.fromarray(self.current_image)
                return image, "✅ 已清除所有标注", self.get_annotation_info()
        return None, "❌ 没有图像", self.get_annotation_info()

    def set_annotation_mode(self, mode):
        """设置标注模式"""
        self.current_annotation_mode = mode
        mode_names = {"positive": "正点", "negative": "负点", "box": "框选"}
        return f"✅ 切换到{mode_names[mode]}模式"
    
    def execute_segmentation(self):
        """执行分割"""
        if self.predictor is None:
            return self.current_image, "❌ 模型未加载"

        if self.current_image is None:
            return None, "❌ 请先加载图像"

        # 检查是否有标注
        total_points = len(self.positive_points) + len(self.negative_points)
        total_boxes = len([b for b in self.boxes if len(b) == 4])

        if total_points == 0 and total_boxes == 0:
            return self.current_image, "❌ 请先添加标注（正点、负点或框选）"

        try:
            # 准备输入数据
            point_coords = None
            point_labels = None
            box = None

            # 处理点标注
            if total_points > 0:
                all_points = []
                all_labels = []

                # 添加正点
                for point in self.positive_points:
                    all_points.append(point)
                    all_labels.append(1)

                # 添加负点
                for point in self.negative_points:
                    all_points.append(point)
                    all_labels.append(0)

                point_coords = np.array(all_points)
                point_labels = np.array(all_labels)

            # 处理框选（只使用第一个完整的框）
            if total_boxes > 0:
                complete_boxes = [b for b in self.boxes if len(b) == 4]
                if complete_boxes:
                    box = np.array(complete_boxes[0])

            # 执行分割
            with torch.inference_mode(), torch.autocast(self.device, dtype=torch.bfloat16):
                masks, scores, _ = self.predictor.predict(
                    point_coords=point_coords,
                    point_labels=point_labels,
                    box=box,
                    multimask_output=True,
                )

            # 选择最佳mask
            best_mask_idx = np.argmax(scores)
            best_mask = masks[best_mask_idx]
            best_score = scores[best_mask_idx]

            # 可视化结果
            result_image = self.visualize_mask_result(self.current_image, best_mask)

            status = f"✅ 分割完成 - 得分: {best_score:.3f} - 使用了 {total_points} 个点, {total_boxes} 个框"
            return result_image, status

        except Exception as e:
            return self.current_image, f"❌ 分割失败: {str(e)}"

    def visualize_annotations(self):
        """可视化当前标注"""
        if self.current_image is None:
            return None

        result = self.current_image.copy()

        # 绘制正点（绿色）
        for point in self.positive_points:
            cv2.circle(result, (int(point[0]), int(point[1])), 8, (0, 255, 0), -1)
            cv2.circle(result, (int(point[0]), int(point[1])), 10, (255, 255, 255), 2)

        # 绘制负点（红色）
        for point in self.negative_points:
            cv2.circle(result, (int(point[0]), int(point[1])), 8, (255, 0, 0), -1)
            cv2.circle(result, (int(point[0]), int(point[1])), 10, (255, 255, 255), 2)

        # 绘制框选
        for box in self.boxes:
            if len(box) == 4:
                # 完整的框
                cv2.rectangle(result, (int(box[0]), int(box[1])), (int(box[2]), int(box[3])), (0, 255, 255), 3)
            elif len(box) == 2:
                # 未完成的框（只有起点）
                cv2.circle(result, (int(box[0]), int(box[1])), 5, (0, 255, 255), -1)

        return Image.fromarray(result)

    def visualize_mask_result(self, image, mask):
        """可视化分割结果"""
        # 创建彩色mask
        color = np.array([30/255, 144/255, 255/255, 0.6])
        h, w = mask.shape[-2:]
        mask_image = mask.reshape(h, w, 1) * color.reshape(1, 1, -1)

        # 叠加到原图
        result = image.copy().astype(np.float32) / 255.0
        result = result * (1 - mask_image[:, :, 3:4]) + mask_image[:, :, :3] * mask_image[:, :, 3:4]
        result = (result * 255).astype(np.uint8)

        # 添加边界
        mask_uint8 = (mask * 255).astype(np.uint8)
        contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        cv2.drawContours(result, contours, -1, (255, 255, 255), 2)

        # 绘制标注点和框
        # 正点（绿色）
        for point in self.positive_points:
            cv2.circle(result, (int(point[0]), int(point[1])), 8, (0, 255, 0), -1)
            cv2.circle(result, (int(point[0]), int(point[1])), 10, (255, 255, 255), 2)

        # 负点（红色）
        for point in self.negative_points:
            cv2.circle(result, (int(point[0]), int(point[1])), 8, (255, 0, 0), -1)
            cv2.circle(result, (int(point[0]), int(point[1])), 10, (255, 255, 255), 2)

        # 框选
        for box in self.boxes:
            if len(box) == 4:
                cv2.rectangle(result, (int(box[0]), int(box[1])), (int(box[2]), int(box[3])), (0, 255, 255), 3)

        return Image.fromarray(result)

# 创建GUI实例
sam2_gui = SAM2GUI()

def create_interface():
    """创建Gradio界面"""
    with gr.Blocks(title="SAM2 植物分割工具", theme=gr.themes.Soft()) as demo:
        gr.Markdown("# 🌱 SAM2 植物分割工具")
        gr.Markdown("使用Segment Anything 2进行植物图像分割")

        with gr.Row():
            with gr.Column(scale=1):
                # 控制面板
                gr.Markdown("## 📁 图像加载")
                directory_input = gr.Textbox(
                    label="图像目录路径",
                    placeholder="输入包含图像的目录路径...",
                    value=""
                )
                load_btn = gr.Button("🔄 加载图像", variant="primary")
                status_text = gr.Textbox(label="状态", interactive=False)

                gr.Markdown("## 🖼️ 图像选择")
                image_dropdown = gr.Dropdown(
                    label="选择图像",
                    choices=[],
                    value=None,
                    interactive=True
                )

                gr.Markdown("## 🖼️ 图像导航")
                with gr.Row():
                    prev_btn = gr.Button("⬅️ 上一张")
                    next_btn = gr.Button("➡️ 下一张")

                image_info = gr.Textbox(label="图像信息", interactive=False)

                with gr.Row():
                    current_num = gr.Number(label="当前", value=0, interactive=False)
                    total_num = gr.Number(label="总数", value=0, interactive=False)

                gr.Markdown("## 🎯 标注工具")
                if HAS_ANNOTATOR:
                    gr.Markdown("💡 **使用方法**: 使用标注工具在图像上添加点和框，然后执行分割")
                else:
                    gr.Markdown("💡 **使用方法**: 选择标注模式，在图像上点击添加标注，然后执行分割")
                    with gr.Row():
                        pos_btn = gr.Button("🟢 正点", variant="secondary")
                        neg_btn = gr.Button("🔴 负点", variant="secondary")
                        box_btn = gr.Button("🟡 框选", variant="secondary")
                    mode_status = gr.Textbox(label="当前模式", interactive=False, value="✅ 切换到正点模式")

                annotation_info = gr.Textbox(label="标注信息", interactive=False, value="正点: 0, 负点: 0, 框选: 0")

                with gr.Row():
                    segment_btn = gr.Button("✂️ 执行分割", variant="primary")
                    clear_btn = gr.Button("🗑️ 清除标注", variant="secondary")

                segment_status = gr.Textbox(label="分割状态", interactive=False)

            with gr.Column(scale=2):
                # 主显示区域
                gr.Markdown("## 🖼️ 主舞台")

                if HAS_ANNOTATOR:
                    # 使用gradio_image_annotator
                    image_annotator = gia.image_annotator(
                        label="图像标注",
                        height=600
                    )
                else:
                    # 使用基础Image组件
                    image_annotator = gr.Image(
                        label="点击图像添加标注",
                        type="pil",
                        interactive=True,
                        height=600
                    )

        # 事件绑定
        load_btn.click(
            sam2_gui.load_images_from_directory,
            inputs=[directory_input],
            outputs=[status_text, image_annotator, image_info, current_num, total_num, image_dropdown]
        )

        image_dropdown.change(
            sam2_gui.select_image_from_dropdown,
            inputs=[image_dropdown],
            outputs=[image_annotator, image_info, current_num, total_num, annotation_info]
        )

        prev_btn.click(
            lambda: sam2_gui.navigate_image("prev"),
            outputs=[image_annotator, image_info, current_num, total_num, image_dropdown, annotation_info]
        )

        next_btn.click(
            lambda: sam2_gui.navigate_image("next"),
            outputs=[image_annotator, image_info, current_num, total_num, image_dropdown, annotation_info]
        )

        if HAS_ANNOTATOR:
            # 使用gradio_image_annotator的事件
            image_annotator.change(
                sam2_gui.process_annotations,
                inputs=[image_annotator],
                outputs=[image_annotator, segment_status, annotation_info]
            )
        else:
            # 标注模式切换（仅在没有annotator时显示）
            pos_btn.click(
                lambda: sam2_gui.set_annotation_mode("positive"),
                outputs=[mode_status]
            )

            neg_btn.click(
                lambda: sam2_gui.set_annotation_mode("negative"),
                outputs=[mode_status]
            )

            box_btn.click(
                lambda: sam2_gui.set_annotation_mode("box"),
                outputs=[mode_status]
            )

            # 图像点击添加标注
            image_annotator.select(
                sam2_gui.add_annotation_legacy,
                outputs=[image_annotator, segment_status, annotation_info]
            )

        # 执行分割
        segment_btn.click(
            sam2_gui.execute_segmentation,
            outputs=[image_annotator, segment_status]
        )

        # 清除标注
        clear_btn.click(
            sam2_gui.clear_all_annotations,
            outputs=[image_annotator, segment_status, annotation_info]
        )

    return demo

if __name__ == "__main__":
    # 创建并启动界面
    demo = create_interface()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        debug=True
    )
