#!/usr/bin/env python3
"""
SAM2 GUI - A Gradio interface for Segment Anything 2
用于植物分割的SAM2图形界面
"""

import os
import sys
import numpy as np
import torch
import gradio as gr
from PIL import Image
import cv2
import glob
from pathlib import Path

# 添加SAM2路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sam2.build_sam import build_sam2
from sam2.sam2_image_predictor import SAM2ImagePredictor

class SAM2GUI:
    def __init__(self):
        self.predictor = None
        self.current_image = None
        self.current_image_path = None
        self.image_list = []
        self.current_index = 0
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        # 初始化SAM2模型
        self.init_sam2_model()
    
    def init_sam2_model(self):
        """初始化SAM2模型"""
        try:
            # 模型配置
            checkpoint = "./checkpoints/sam2.1_hiera_large.pt"
            model_cfg = "configs/sam2.1/sam2.1_hiera_l.yaml"
            
            # 检查模型文件是否存在
            if not os.path.exists(checkpoint):
                raise FileNotFoundError(f"模型文件不存在: {checkpoint}")
            
            # 构建模型
            sam2_model = build_sam2(model_cfg, checkpoint, device=self.device)
            self.predictor = SAM2ImagePredictor(sam2_model)
            
            print(f"SAM2模型已加载到设备: {self.device}")
            return "✅ SAM2模型加载成功"
            
        except Exception as e:
            error_msg = f"❌ 模型加载失败: {str(e)}"
            print(error_msg)
            return error_msg
    
    def load_images_from_directory(self, directory_path):
        """从目录加载所有图像"""
        if not directory_path or not os.path.exists(directory_path):
            return "❌ 目录路径无效", None, "没有图像", 0, 0
        
        # 支持的图像格式
        image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.tif']
        
        # 获取所有图像文件
        image_files = []
        for ext in image_extensions:
            image_files.extend(glob.glob(os.path.join(directory_path, ext)))
            image_files.extend(glob.glob(os.path.join(directory_path, ext.upper())))
        
        # 排序文件列表
        image_files.sort()
        
        if not image_files:
            return "❌ 目录中没有找到图像文件", None, "没有图像", 0, 0
        
        self.image_list = image_files
        self.current_index = 0
        
        # 加载第一张图像
        first_image, info = self.load_image_by_index(0)
        
        status = f"✅ 成功加载 {len(image_files)} 张图像"
        return status, first_image, info, 1, len(image_files)
    
    def load_image_by_index(self, index):
        """根据索引加载图像"""
        if not self.image_list or index < 0 or index >= len(self.image_list):
            return None, "无效的图像索引"
        
        try:
            image_path = self.image_list[index]
            image = Image.open(image_path).convert("RGB")
            
            # 保存当前图像信息
            self.current_image = np.array(image)
            self.current_image_path = image_path
            self.current_index = index
            
            # 设置图像到预测器
            if self.predictor:
                with torch.inference_mode(), torch.autocast(self.device, dtype=torch.bfloat16):
                    self.predictor.set_image(self.current_image)
            
            # 图像信息
            filename = os.path.basename(image_path)
            info = f"图像: {filename} ({index + 1}/{len(self.image_list)}) - 尺寸: {image.size}"
            
            return image, info
            
        except Exception as e:
            return None, f"❌ 加载图像失败: {str(e)}"
    
    def navigate_image(self, direction):
        """导航图像 (上一张/下一张)"""
        if not self.image_list:
            return None, "没有加载图像", 1, len(self.image_list) if self.image_list else 0
        
        if direction == "prev":
            new_index = max(0, self.current_index - 1)
        else:  # next
            new_index = min(len(self.image_list) - 1, self.current_index + 1)
        
        image, info = self.load_image_by_index(new_index)
        return image, info, new_index + 1, len(self.image_list)
    
    def segment_with_click(self, evt: gr.SelectData):
        """处理图像点击事件进行分割"""
        if self.predictor is None:
            return self.current_image, "❌ 模型未加载"

        if self.current_image is None:
            return None, "❌ 请先加载图像"

        try:
            # 获取点击坐标
            x, y = evt.index[0], evt.index[1]
            points = np.array([[x, y]])
            labels = np.array([1])  # 1表示前景点

            # 执行分割
            with torch.inference_mode(), torch.autocast(self.device, dtype=torch.bfloat16):
                masks, scores, _ = self.predictor.predict(
                    point_coords=points,
                    point_labels=labels,
                    multimask_output=True,
                )

            # 选择最佳mask (得分最高的)
            best_mask_idx = np.argmax(scores)
            best_mask = masks[best_mask_idx]
            best_score = scores[best_mask_idx]

            # 创建可视化结果
            result_image = self.visualize_mask(self.current_image, best_mask, points)

            status = f"✅ 分割完成 - 点击位置: ({x}, {y}) - 得分: {best_score:.3f}"
            return result_image, status

        except Exception as e:
            return self.current_image, f"❌ 分割失败: {str(e)}"
    
    def visualize_mask(self, image, mask, points=None):
        """可视化分割结果"""
        # 创建彩色mask
        color = np.array([30/255, 144/255, 255/255, 0.6])
        h, w = mask.shape[-2:]
        mask_image = mask.reshape(h, w, 1) * color.reshape(1, 1, -1)
        
        # 叠加到原图
        result = image.copy().astype(np.float32) / 255.0
        result = result * (1 - mask_image[:, :, 3:4]) + mask_image[:, :, :3] * mask_image[:, :, 3:4]
        result = (result * 255).astype(np.uint8)
        
        # 添加边界
        mask_uint8 = (mask * 255).astype(np.uint8)
        contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        cv2.drawContours(result, contours, -1, (255, 255, 255), 2)
        
        # 绘制点击点
        if points is not None:
            for point in points:
                cv2.circle(result, (int(point[0]), int(point[1])), 8, (0, 255, 0), -1)
                cv2.circle(result, (int(point[0]), int(point[1])), 8, (255, 255, 255), 2)
        
        return Image.fromarray(result)

# 创建GUI实例
sam2_gui = SAM2GUI()

def create_interface():
    """创建Gradio界面"""
    with gr.Blocks(title="SAM2 植物分割工具", theme=gr.themes.Soft()) as demo:
        gr.Markdown("# 🌱 SAM2 植物分割工具")
        gr.Markdown("使用Segment Anything 2进行植物图像分割")
        
        with gr.Row():
            with gr.Column(scale=1):
                # 控制面板
                gr.Markdown("## 📁 图像加载")
                directory_input = gr.Textbox(
                    label="图像目录路径",
                    placeholder="输入包含图像的目录路径...",
                    value=""
                )
                load_btn = gr.Button("🔄 加载图像", variant="primary")
                status_text = gr.Textbox(label="状态", interactive=False)
                
                gr.Markdown("## 🖼️ 图像导航")
                with gr.Row():
                    prev_btn = gr.Button("⬅️ 上一张")
                    next_btn = gr.Button("➡️ 下一张")
                
                image_info = gr.Textbox(label="图像信息", interactive=False)
                
                with gr.Row():
                    current_num = gr.Number(label="当前", value=0, interactive=False)
                    total_num = gr.Number(label="总数", value=0, interactive=False)
                
                gr.Markdown("## 🎯 分割说明")
                gr.Markdown("💡 **使用方法**: 直接点击图像中的植物部分进行分割")
                segment_status = gr.Textbox(label="分割状态", interactive=False)
                
            with gr.Column(scale=2):
                # 主显示区域
                gr.Markdown("## 🖼️ 主舞台")
                image_display = gr.Image(
                    label="点击图像添加分割点",
                    type="pil",
                    interactive=True
                )
        
        # 事件绑定
        load_btn.click(
            sam2_gui.load_images_from_directory,
            inputs=[directory_input],
            outputs=[status_text, image_display, image_info, current_num, total_num]
        )
        
        prev_btn.click(
            lambda: sam2_gui.navigate_image("prev"),
            outputs=[image_display, image_info, current_num, total_num]
        )
        
        next_btn.click(
            lambda: sam2_gui.navigate_image("next"),
            outputs=[image_display, image_info, current_num, total_num]
        )
        
        image_display.select(
            sam2_gui.segment_with_click,
            outputs=[image_display, segment_status]
        )
    
    return demo

if __name__ == "__main__":
    # 创建并启动界面
    demo = create_interface()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        debug=True
    )
