#!/usr/bin/env python3
"""
SAM2 GUI 启动器
确保在正确的环境和目录中启动GUI
"""

import os
import sys
import subprocess

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要Python 3.8+")
        return False
    
    # 检查必要的包
    required_packages = ['torch', 'gradio', 'numpy', 'cv2', 'PIL']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少必要的包: {', '.join(missing_packages)}")
        print("请运行: pip install gradio torch torchvision opencv-python pillow numpy")
        return False
    
    print("✅ 环境检查通过")
    return True

def check_files():
    """检查必要文件"""
    print("🔍 检查必要文件...")
    
    # 切换到正确的目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(script_dir)
    os.chdir(parent_dir)
    
    required_files = [
        './checkpoints/sam2.1_hiera_large.pt',
        './sam2/configs/sam2.1/sam2.1_hiera_l.yaml',
        './gui/sam2_gui.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 文件检查通过")
    return True

def launch_gui():
    """启动GUI"""
    print("🚀 启动SAM2 GUI...")
    
    try:
        # 导入并运行GUI
        sys.path.insert(0, '.')
        from gui.sam2_gui import create_interface
        
        print("✅ GUI模块加载成功")
        print("🌐 正在启动Gradio界面...")
        print("📝 使用说明请查看: gui/demo_instructions.md")
        
        demo = create_interface()
        demo.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False,
            debug=False,
            show_error=True
        )
        
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")
        print("\n🔧 故障排除建议:")
        print("1. 确保已激活正确的conda环境")
        print("2. 检查所有依赖是否正确安装")
        print("3. 确保模型文件已下载到checkpoints目录")
        return False
    
    return True

def main():
    """主函数"""
    print("🌱 SAM2 植物分割工具启动器")
    print("=" * 50)
    
    # 检查环境
    if not check_environment():
        sys.exit(1)
    
    # 检查文件
    if not check_files():
        sys.exit(1)
    
    # 启动GUI
    if not launch_gui():
        sys.exit(1)

if __name__ == "__main__":
    main()
