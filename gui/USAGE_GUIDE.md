# SAM2 植物分割GUI - 完整使用指南

## 🎉 新功能亮点

### ✨ 已实现的功能

1. **🖼️ 缩略图浏览**
   - 加载图像后自动生成缩略图网格
   - 当前选中图像有绿色边框标识
   - 点击缩略图快速切换图像

2. **🎯 多种标注模式**
   - **🟢 正点模式**: 标记前景区域（要分割的部分）
   - **🔴 负点模式**: 标记背景区域（不要分割的部分）
   - **🟡 框选模式**: 用矩形框大致圈定目标区域

3. **🔧 智能标注管理**
   - 实时显示标注数量统计
   - 支持混合使用多种标注方式
   - 一键清除所有标注

4. **✂️ 精确分割**
   - 基于所有标注执行SAM2分割
   - 保留标注点在结果图上
   - 高质量分割结果可视化

## 🚀 快速开始

### 1. 启动GUI
```bash
# 确保在sam2环境中
conda activate sam2

# 启动GUI
python gui/launch_gui.py
```

### 2. 基本操作流程

#### 步骤1: 加载图像
1. 在"图像目录路径"输入框中输入包含图像的文件夹路径
2. 点击"🔄 加载图像"按钮
3. 左侧会显示缩略图网格，右侧显示第一张图像

#### 步骤2: 选择图像
- **方法1**: 点击左侧缩略图直接选择
- **方法2**: 使用"⬅️ 上一张"/"➡️ 下一张"按钮导航

#### 步骤3: 添加标注
1. 选择标注模式：
   - 点击"🟢 正点"：标记要分割的区域
   - 点击"🔴 负点"：标记不要分割的区域  
   - 点击"🟡 框选"：用矩形框圈定区域

2. 在图像上点击添加标注：
   - **正点/负点**: 直接点击即可添加
   - **框选**: 点击两次确定矩形的对角

3. 观察标注信息更新

#### 步骤4: 执行分割
1. 确认标注完成后，点击"✂️ 执行分割"
2. 等待分割完成，查看结果
3. 如果不满意，可以添加更多标注或清除重新开始

## 🎯 高级使用技巧

### 植物分割最佳实践

#### 🌱 单个植物分割
1. **粗定位**: 使用框选模式大致圈住整个植物
2. **精确标记**: 在植物主干或叶片中心添加正点
3. **排除干扰**: 在背景、土壤等区域添加负点
4. **边界优化**: 在边界模糊处添加正负点进行精细调整

#### 🌿 复杂场景分割
1. **多正点策略**: 在植物的不同部位（叶片、茎干、花朵）添加多个正点
2. **负点排除**: 在其他植物、杂草、阴影等干扰区域添加负点
3. **框选辅助**: 用框选限定大致范围，避免分割到远处物体

### 标注组合策略

#### 🎯 推荐组合
- **简单场景**: 1-2个正点 + 框选
- **复杂背景**: 多个正点 + 多个负点
- **精细分割**: 框选 + 正点 + 负点的完整组合

#### ⚠️ 注意事项
- 正点应该点在目标物体的典型区域
- 负点要点在明确不属于目标的区域
- 框选不需要很精确，大致包含目标即可
- 可以多次尝试不同的标注组合

## 🔧 界面功能详解

### 左侧控制面板

#### 📁 图像加载区
- **目录路径输入**: 支持绝对路径和相对路径
- **加载按钮**: 扫描目录中的所有图像文件
- **状态显示**: 显示加载结果和图像数量

#### 🖼️ 缩略图区
- **网格显示**: 3列网格布局，自动滚动
- **选中标识**: 当前图像有绿色边框
- **快速切换**: 点击任意缩略图立即切换

#### 🧭 导航控制
- **上一张/下一张**: 按顺序浏览图像
- **图像信息**: 显示文件名、序号、尺寸
- **计数器**: 当前位置/总数量

#### 🎯 标注工具
- **模式按钮**: 切换正点/负点/框选模式
- **标注统计**: 实时显示各类标注数量
- **模式状态**: 显示当前选中的标注模式

#### ✂️ 分割控制
- **执行分割**: 基于当前标注进行分割
- **清除标注**: 删除所有标注，重新开始

### 右侧主舞台
- **图像显示**: 高分辨率图像显示
- **交互标注**: 根据模式点击添加标注
- **结果预览**: 显示分割结果和标注

## 🐛 常见问题解决

### 分割效果问题
- **分割过大**: 添加负点排除多余区域
- **分割过小**: 添加更多正点包含遗漏部分
- **边界不准**: 在边界附近添加正负点调整
- **多个物体**: 使用负点分离不同物体

### 操作问题
- **标注看不清**: 放大浏览器页面
- **误操作**: 使用"清除标注"重新开始
- **切换图像**: 标注会自动清除，这是正常的

### 性能问题
- **加载慢**: 图像数量多时需要等待
- **分割慢**: 大图像或复杂标注需要更多时间
- **内存不足**: 减少同时处理的图像数量

## 📝 快捷操作

- **快速浏览**: 使用缩略图而不是导航按钮
- **批量处理**: 处理完一张图像后直接点击下一张缩略图
- **模式切换**: 可以在添加标注过程中随时切换模式
- **结果保存**: 右键点击结果图像可能有保存选项（取决于浏览器）

## 🎯 总结

这个增强版的SAM2 GUI提供了：
1. ✅ 直观的缩略图浏览
2. ✅ 灵活的多模式标注
3. ✅ 智能的分割执行
4. ✅ 友好的用户界面

现在你可以更高效地处理大量植物图像，获得更精确的分割结果！
