#!/usr/bin/env python3
"""
测试GUI功能的脚本
"""

import os
import sys
import tempfile
import numpy as np
from PIL import Image

# 添加SAM2路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def create_test_images():
    """创建测试图像"""
    test_dir = tempfile.mkdtemp()
    print(f"创建测试图像目录: {test_dir}")
    
    # 创建几张测试图像
    for i in range(3):
        # 创建随机彩色图像
        img_array = np.random.randint(0, 255, (300, 400, 3), dtype=np.uint8)
        img = Image.fromarray(img_array)
        img_path = os.path.join(test_dir, f"test_image_{i+1}.jpg")
        img.save(img_path)
        print(f"创建测试图像: {img_path}")
    
    return test_dir

def test_gui_functions():
    """测试GUI核心功能"""
    print("🔍 测试GUI核心功能...")
    
    try:
        from gui.sam2_gui import SAM2GUI
        
        # 创建GUI实例
        gui = SAM2GUI()
        print("✅ GUI实例创建成功")
        
        # 创建测试图像
        test_dir = create_test_images()
        
        # 测试图像加载
        result = gui.load_images_from_directory(test_dir)
        if len(result) == 6:  # 应该返回6个值
            status, image, info, current, total, thumbnails = result
            print(f"✅ 图像加载测试通过: {status}")
            print(f"   图像信息: {info}")
            print(f"   当前/总数: {current}/{total}")
            print(f"   缩略图数量: {len(thumbnails)}")
        else:
            print(f"❌ 图像加载返回值数量错误: 期望6个，实际{len(result)}个")
            return False
        
        # 测试标注功能
        gui.set_annotation_mode("positive")
        gui.positive_points = [[100, 100], [200, 200]]
        gui.negative_points = [[50, 50]]
        
        info = gui.get_annotation_info()
        print(f"✅ 标注信息测试通过: {info}")
        
        # 测试清除标注
        gui.clear_all_annotations()
        if len(gui.positive_points) == 0 and len(gui.negative_points) == 0:
            print("✅ 清除标注测试通过")
        else:
            print("❌ 清除标注测试失败")
            return False
        
        print("🎉 所有GUI功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ GUI功能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 SAM2 GUI 功能测试")
    print("=" * 40)
    
    # 切换到正确的目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(script_dir)
    os.chdir(parent_dir)
    
    success = test_gui_functions()
    
    if success:
        print("\n✅ 所有测试通过！GUI已准备就绪。")
        print("🚀 可以运行: python gui/launch_gui.py")
    else:
        print("\n❌ 测试失败，请检查错误信息。")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
