#!/usr/bin/env python3
"""
测试SAM2 GUI安装的脚本
"""

import os
import sys
import torch

def test_installation():
    """测试安装是否正确"""
    print("🔍 测试SAM2 GUI安装...")
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查PyTorch
    try:
        print(f"PyTorch版本: {torch.__version__}")
        print(f"CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"CUDA版本: {torch.version.cuda}")
            print(f"GPU数量: {torch.cuda.device_count()}")
            print(f"当前GPU: {torch.cuda.get_device_name()}")
    except Exception as e:
        print(f"❌ PyTorch检查失败: {e}")
        return False
    
    # 检查必要的包
    required_packages = [
        'gradio',
        'numpy',
        'PIL',
        'cv2'
    ]
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"❌ {package} 未安装")
            return False
    
    # 检查文件结构
    required_files = [
        './checkpoints/sam2.1_hiera_large.pt',
        './sam2/configs/sam2.1/sam2.1_hiera_l.yaml',
        './sam2/__init__.py',
        './sam2/build_sam.py',
        './sam2/sam2_image_predictor.py'
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} 存在")
        else:
            print(f"❌ {file_path} 不存在")
            return False
    
    # 测试SAM2导入
    try:
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from sam2.build_sam import build_sam2
        from sam2.sam2_image_predictor import SAM2ImagePredictor
        print("✅ SAM2模块导入成功")
    except Exception as e:
        print(f"❌ SAM2模块导入失败: {e}")
        return False
    
    # 测试模型加载
    try:
        checkpoint = "./checkpoints/sam2.1_hiera_large.pt"
        model_cfg = "configs/sam2.1/sam2.1_hiera_l.yaml"
        device = "cuda" if torch.cuda.is_available() else "cpu"
        
        sam2_model = build_sam2(model_cfg, checkpoint, device=device)
        predictor = SAM2ImagePredictor(sam2_model)
        print(f"✅ SAM2模型加载成功 (设备: {device})")
    except Exception as e:
        print(f"❌ SAM2模型加载失败: {e}")
        return False
    
    print("\n🎉 所有测试通过！SAM2 GUI已准备就绪。")
    print("\n📝 使用方法:")
    print("   python gui/run_gui.py")
    print("   或")
    print("   python gui/sam2_gui.py")
    
    return True

if __name__ == "__main__":
    # 切换到正确的目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    os.chdir(parent_dir)
    
    success = test_installation()
    sys.exit(0 if success else 1)
