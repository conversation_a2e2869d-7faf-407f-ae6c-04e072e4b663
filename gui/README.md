# SAM2 植物分割GUI工具

这是一个基于Gradio的SAM2图形界面，专门用于植物图像的分割任务。

## 功能特点

- 🖼️ **批量图像加载**: 从本地目录加载多张图像
- 🖱️ **缩略图浏览**: 缩略图网格显示，点击快速切换图像
- 🎯 **多种标注方式**: 支持正点、负点和框选三种标注模式
- ✂️ **智能分割**: 基于标注执行SAM2分割
- 🔄 **图像导航**: 轻松浏览多张图像
- 🌱 **植物优化**: 针对植物分割进行优化
- 🚀 **GPU加速**: 自动使用CUDA加速（如果可用）

## 安装要求

### 1. 环境准备
```bash
# 创建conda环境
conda create -n sam2 python=3.9
conda activate sam2

# 安装依赖
pip install -r gui/requirements.txt
```

### 2. SAM2安装
确保已经按照主项目的安装说明安装了SAM2。

### 3. 模型文件
确保已下载模型文件：
- `checkpoints/sam2.1_hiera_large.pt`

## 使用方法

### 启动GUI
```bash
# 方法1: 直接运行GUI脚本
python gui/run_gui.py

# 方法2: 运行主GUI文件
cd gui
python sam2_gui.py
```

### 使用步骤

1. **启动界面**: 运行上述命令后，浏览器会自动打开GUI界面

2. **加载图像**: 
   - 在"图像目录路径"输入框中输入包含图像的目录路径
   - 点击"🔄 加载图像"按钮

3. **浏览图像**:
   - 使用"⬅️ 上一张"和"➡️ 下一张"按钮浏览图像
   - 查看图像信息和当前位置

4. **添加标注**:
   - 选择标注模式：🟢正点（前景）、🔴负点（背景）或🟡框选
   - 在图像上点击添加正点或负点
   - 框选模式：点击两次确定矩形框的对角
   - 可以混合使用多种标注方式

5. **执行分割**:
   - 添加标注后，点击"✂️ 执行分割"按钮
   - 系统会基于所有标注执行SAM2分割
   - 分割结果会以彩色蒙版叠加在原图上
   - 标注点和框会保留显示

## 界面说明

### 左侧控制面板
- **📁 图像加载**: 输入目录路径并加载图像
- **🖼️ 缩略图**: 网格显示所有图像，点击快速切换
- **🖼️ 图像导航**: 上一张/下一张按钮浏览图像
- **🎯 标注工具**: 选择标注模式（正点/负点/框选）
- **✂️ 分割控制**: 执行分割和清除标注按钮

### 右侧主舞台
- **图像显示区域**: 显示当前图像、标注和分割结果
- **交互标注**: 根据选择的模式点击添加标注

## 技术特点

- **SAM2模型**: 使用最新的Segment Anything 2模型
- **实时分割**: 点击即分割，响应迅速
- **可视化**: 清晰的分割结果可视化
- **批处理**: 支持批量处理多张图像

## 注意事项

1. **GPU内存**: 建议使用至少8GB显存的GPU
2. **图像格式**: 支持JPG、PNG、BMP、TIFF等常见格式
3. **分割精度**: 点击位置会影响分割结果，建议点击目标物体的中心区域
4. **模型加载**: 首次启动时模型加载可能需要一些时间

## 故障排除

### 常见问题

1. **模型加载失败**
   - 检查模型文件路径是否正确
   - 确认GPU驱动和CUDA版本兼容

2. **图像加载失败**
   - 检查目录路径是否存在
   - 确认目录中包含支持的图像格式

3. **分割效果不佳**
   - 尝试点击目标物体的不同位置
   - 确保图像质量良好，对比度清晰

## 开发信息

- **框架**: Gradio + PyTorch
- **模型**: SAM2.1 Hiera Large
- **设备**: 自动检测CUDA/CPU
