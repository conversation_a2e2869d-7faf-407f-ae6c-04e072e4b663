# SAM2 植物分割GUI工具

这是一个基于Gradio的SAM2图形界面，专门用于植物图像的分割任务。

## 功能特点

- 🖼️ **批量图像加载**: 从本地目录加载多张图像
- 🎯 **点击分割**: 直接点击图像进行智能分割
- 🔄 **图像导航**: 轻松浏览多张图像
- 🌱 **植物优化**: 针对植物分割进行优化
- 🚀 **GPU加速**: 自动使用CUDA加速（如果可用）

## 安装要求

### 1. 环境准备
```bash
# 创建conda环境
conda create -n sam2 python=3.9
conda activate sam2

# 安装依赖
pip install -r gui/requirements.txt
```

### 2. SAM2安装
确保已经按照主项目的安装说明安装了SAM2。

### 3. 模型文件
确保已下载模型文件：
- `checkpoints/sam2.1_hiera_large.pt`

## 使用方法

### 启动GUI
```bash
# 方法1: 直接运行GUI脚本
python gui/run_gui.py

# 方法2: 运行主GUI文件
cd gui
python sam2_gui.py
```

### 使用步骤

1. **启动界面**: 运行上述命令后，浏览器会自动打开GUI界面

2. **加载图像**: 
   - 在"图像目录路径"输入框中输入包含图像的目录路径
   - 点击"🔄 加载图像"按钮

3. **浏览图像**:
   - 使用"⬅️ 上一张"和"➡️ 下一张"按钮浏览图像
   - 查看图像信息和当前位置

4. **执行分割**:
   - 直接点击图像中要分割的植物部分
   - 系统会自动执行分割并显示结果
   - 分割结果会以彩色蒙版叠加在原图上

## 界面说明

### 左侧控制面板
- **📁 图像加载**: 输入目录路径并加载图像
- **🖼️ 图像导航**: 浏览已加载的图像
- **🎯 分割说明**: 使用说明和状态显示

### 右侧主舞台
- **图像显示区域**: 显示当前图像和分割结果
- **点击交互**: 直接点击图像进行分割

## 技术特点

- **SAM2模型**: 使用最新的Segment Anything 2模型
- **实时分割**: 点击即分割，响应迅速
- **可视化**: 清晰的分割结果可视化
- **批处理**: 支持批量处理多张图像

## 注意事项

1. **GPU内存**: 建议使用至少8GB显存的GPU
2. **图像格式**: 支持JPG、PNG、BMP、TIFF等常见格式
3. **分割精度**: 点击位置会影响分割结果，建议点击目标物体的中心区域
4. **模型加载**: 首次启动时模型加载可能需要一些时间

## 故障排除

### 常见问题

1. **模型加载失败**
   - 检查模型文件路径是否正确
   - 确认GPU驱动和CUDA版本兼容

2. **图像加载失败**
   - 检查目录路径是否存在
   - 确认目录中包含支持的图像格式

3. **分割效果不佳**
   - 尝试点击目标物体的不同位置
   - 确保图像质量良好，对比度清晰

## 开发信息

- **框架**: Gradio + PyTorch
- **模型**: SAM2.1 Hiera Large
- **设备**: 自动检测CUDA/CPU
