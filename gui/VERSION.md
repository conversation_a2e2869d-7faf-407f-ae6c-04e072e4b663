# SAM2 GUI 版本信息

## 版本 2.0 - 增强版 (当前版本)

### 🎉 新增功能
- ✅ **缩略图浏览**: 网格显示所有图像，点击快速切换
- ✅ **多种标注模式**: 支持正点、负点、框选三种标注方式
- ✅ **智能标注管理**: 实时统计、混合标注、一键清除
- ✅ **增强分割**: 基于多种标注的精确分割
- ✅ **改进界面**: 更直观的用户界面和操作流程

### 🔧 技术改进
- 重构了标注系统架构
- 优化了图像加载和显示逻辑
- 改进了分割结果可视化
- 增强了错误处理和用户反馈

### 📁 文件结构
```
gui/
├── sam2_gui.py           # 主GUI程序
├── launch_gui.py         # 启动器
├── test_installation.py  # 安装测试
├── test_gui_functions.py # 功能测试
├── requirements.txt      # 依赖列表
├── README.md            # 基本说明
├── USAGE_GUIDE.md       # 详细使用指南
├── demo_instructions.md # 演示说明
├── VERSION.md           # 版本信息
└── start_gui.sh         # Shell启动脚本
```

---

## 版本 1.0 - 基础版

### 🎯 基础功能
- ✅ **图像加载**: 从目录批量加载图像
- ✅ **图像导航**: 上一张/下一张浏览
- ✅ **点击分割**: 单点点击即时分割
- ✅ **结果显示**: 基本的分割结果可视化

### 📝 版本历史
- 初始版本实现了基本的SAM2分割功能
- 支持CUDA加速
- 简单的Gradio界面

---

## 🚀 未来计划

### 版本 3.0 规划
- [ ] **批量处理**: 一键处理整个目录的所有图像
- [ ] **结果导出**: 支持多种格式的mask导出
- [ ] **历史记录**: 保存和恢复标注历史
- [ ] **快捷键**: 键盘快捷键支持
- [ ] **预设模板**: 常用标注模式的预设

### 长期目标
- [ ] **视频支持**: 扩展到视频序列分割
- [ ] **模型选择**: 支持不同的SAM2模型
- [ ] **插件系统**: 支持自定义扩展
- [ ] **云端部署**: 支持远程访问

---

## 🐛 已知问题

### 当前版本
- 大图像可能导致内存占用较高
- 缩略图生成可能需要一些时间
- 浏览器兼容性可能存在差异

### 修复计划
- 优化内存使用
- 异步缩略图生成
- 增强浏览器兼容性测试

---

## 📞 支持信息

### 技术栈
- **后端**: Python 3.8+, PyTorch, SAM2
- **前端**: Gradio 4.0+
- **图像处理**: OpenCV, PIL, NumPy
- **GPU**: CUDA 支持

### 系统要求
- **操作系统**: Linux, macOS, Windows
- **Python**: 3.8 或更高版本
- **GPU**: 推荐 8GB+ 显存
- **内存**: 推荐 16GB+ RAM

### 更新日志
- 2024-XX-XX: v2.0 发布，新增缩略图和多标注模式
- 2024-XX-XX: v1.0 发布，基础分割功能
