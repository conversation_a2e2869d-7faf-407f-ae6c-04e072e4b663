#!/bin/bash

# 激活conda环境并启动GUI
echo "🌱 启动SAM2植物分割工具..."

# 检查是否在sam2环境中
if [[ "$CONDA_DEFAULT_ENV" != "sam2" ]]; then
    echo "⚠️  请先激活sam2环境: conda activate sam2"
    exit 1
fi

# 切换到正确的目录
cd "$(dirname "$0")/.."

# 检查必要文件
if [ ! -f "./checkpoints/sam2.1_hiera_large.pt" ]; then
    echo "❌ 模型文件不存在: ./checkpoints/sam2.1_hiera_large.pt"
    echo "请确保已下载模型文件到checkpoints目录"
    exit 1
fi

echo "✅ 文件检查通过"
echo "🚀 启动Gradio界面..."

# 启动GUI
python gui/sam2_gui.py
