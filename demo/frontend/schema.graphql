input AddPointsInput {
  sessionId: String!
  frameIndex: Int!
  clearOldPoints: Boolean!
  objectId: Int!
  labels: [Int!]!
  points: [[Float!]!]!
}

type CancelPropagateInVideo {
  success: Boolean!
}

input CancelPropagateInVideoInput {
  sessionId: String!
}

input ClearPointsInFrameInput {
  sessionId: String!
  frameIndex: Int!
  objectId: Int!
}

type ClearPointsInVideo {
  success: Boolean!
}

input ClearPointsInVideoInput {
  sessionId: String!
}

type CloseSession {
  success: Boolean!
}

input CloseSessionInput {
  sessionId: String!
}

type Mutation {
  startSession(input: StartSessionInput!): StartSession!
  closeSession(input: CloseSessionInput!): CloseSession!
  addPoints(input: AddPointsInput!): RLEMaskListOnFrame!
  clearPointsInFrame(input: ClearPointsInFrameInput!): RLEMaskListOnFrame!
  clearPointsInVideo(input: ClearPointsInVideoInput!): ClearPointsInVideo!
  removeObject(input: RemoveObjectInput!): [RLEMaskListOnFrame!]!
  cancelPropagateInVideo(
    input: CancelPropagateInVideoInput!
  ): CancelPropagateInVideo!
  createDeletionId: String!
  acceptTos: Boolean!
  acceptTermsOfService: String!
  uploadVideo(
    file: Upload!
    startTimeSec: Float = null
    durationTimeSec: Float = null
  ): Video!
  uploadSharedVideo(file: Upload!): SharedVideo!
  uploadAnnotations(file: Upload!): Boolean!
}

input PingInput {
  sessionId: String!
}

type Pong {
  success: Boolean!
}

type Query {
  ping(input: PingInput!): Pong!
  defaultVideo: Video!
  videos(
    """
    Returns the items in the list that come before the specified cursor.
    """
    before: String = null
    """
    Returns the items in the list that come after the specified cursor.
    """
    after: String = null
    """
    Returns the first n items from the list.
    """
    first: Int = null
    """
    Returns the items in the list that come after the specified cursor.
    """
    last: Int = null
  ): VideoConnection!
  sharedVideo(path: String!): SharedVideo!
}

type RLEMask {
  size: [Int!]!
  counts: String!
  order: String!
}

type RLEMaskForObject {
  objectId: Int!
  rleMask: RLEMask!
}

type RLEMaskListOnFrame {
  frameIndex: Int!
  rleMaskList: [RLEMaskForObject!]!
}

input RemoveObjectInput {
  sessionId: String!
  objectId: Int!
}

type StartSession {
  sessionId: String!
}

input StartSessionInput {
  path: String!
}

"""
The `ID` scalar type represents a unique identifier, often used to refetch an object or as key for a cache. The ID type appears in a JSON response as a String; however, it is not intended to be human-readable. When expected as an input type, any string (such as `"4"`) or integer (such as `4`) input value will be accepted as an ID.
"""
scalar GlobalID
  @specifiedBy(url: "https://relay.dev/graphql/objectidentification.htm")

"""
An object with a Globally Unique ID
"""
interface Node {
  """
  The Globally Unique ID of this object
  """
  id: GlobalID!
}

"""
Information to aid in pagination.
"""
type PageInfo {
  """
  When paginating forwards, are there more items?
  """
  hasNextPage: Boolean!
  """
  When paginating backwards, are there more items?
  """
  hasPreviousPage: Boolean!
  """
  When paginating backwards, the cursor to continue.
  """
  startCursor: String
  """
  When paginating forwards, the cursor to continue.
  """
  endCursor: String
}

type SharedVideo {
  path: String!
  url: String!
}

scalar Upload

type Video implements Node {
  """
  The Globally Unique ID of this object
  """
  id: GlobalID!
  path: String!
  posterPath: String
  width: Int!
  height: Int!
  url: String!
  posterUrl: String!
}

"""
A connection to a list of items.
"""
type VideoConnection {
  """
  Pagination data for this connection
  """
  pageInfo: PageInfo!
  """
  Contains the nodes in this connection
  """
  edges: [VideoEdge!]!
}

"""
An edge in a connection.
"""
type VideoEdge {
  """
  A cursor for use in pagination
  """
  cursor: String!
  """
  The item at the end of the edge
  """
  node: Video!
}

schema {
  query: Query
  mutation: Mutation
}
