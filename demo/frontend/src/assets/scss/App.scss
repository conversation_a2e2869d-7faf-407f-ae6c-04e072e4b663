/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * 
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * 
 *     http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
@tailwind base;
@tailwind components;
@tailwind utilities;

.tab {
  display: flex;
  padding: 0px 0px;
  margin-right: 6px;
  align-items: center;
  height: 100%;
}

@layer base {
  @font-face {
    font-family: 'Inter';
    src: url(/fonts/Inter-VariableFont.ttf) format('truetype-variations');
  }
}

body {
  font-family: 'Inter', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body,
html,
#root {
  height: 100%;
  @media screen and (max-width: '768px') {
    overflow: hidden;
  }
}

:root {
  --segEv-font: 'Inter', system-ui, -apple-system, BlinkMacSystemFont,
    'Segoe UI', Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue',
    sans-serif;
  --perspective: 4000px;
  color-scheme: dark;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: 'Inter', sans-serif;
}

.prose .display h1 {
  @apply text-4xl text-gray-800 font-medium leading-tight;
}

.prose .display h2 {
  @apply text-gray-800 font-medium leading-tight;
  font-size: 2.5rem;
}

.prose h1 {
  @apply text-3xl text-gray-800 font-medium leading-tight mt-2 mb-4;
  letter-spacing: 0.016rem;
}

.prose h2 {
  @apply text-2xl text-gray-800 font-medium leading-tight my-2;
  letter-spacing: 0.01rem;
}

.prose h3 {
  @apply text-xl text-gray-800 font-medium leading-tight my-2;
  letter-spacing: 0.005rem;
}

.prose h4 {
  @apply text-lg text-gray-800 font-medium leading-tight my-2;
}

.prose h5 {
  @apply text-xl text-gray-700 font-normal leading-normal my-2;
  letter-spacing: 0.005rem;
}

.prose h6 {
  @apply text-base text-gray-700 font-normal leading-normal my-2;
}

.prose p {
  @apply text-sm text-gray-700 font-normal leading-normal;
  @apply leading-snug;
}

.prose ol,
.prose ul {
  @apply text-sm text-gray-700 font-normal leading-normal;
  padding-right: 2rem;
}

.dark-mode h1,
.dark-mode h2,
.dark-mode h3,
.dark-mode h4,
.dark-mode h5,
.dark-mode p,
.dark-mode ol,
.dark-mode ul,
.dark-mode p *,
.dark-mode ol *,
.dark-mode ul *,
≈ {
  @apply text-white;
}

.dark-mode h4,
.dark-mode h6,
.dark-mode li::marker,
.dark-mode a {
  @apply text-gray-200;
}

.flex-grow-2 {
  flex-grow: 2;
}

.flex-grow-3 {
  flex-grow: 3;
}

.flex-grow-4 {
  flex-grow: 4;
}

.flex-grow-5 {
  flex-grow: 5;
}

.nav-title {
  font-family: var(--segEv-font);
}

.segment-active {
  animation: segment-highlight 2s linear infinite;
  stroke-dasharray: 5, 10;
  stroke-width: 4px;
}

@keyframes segment-highlight {
  to {
    stroke-dashoffset: 60;
  }
}

.segment-select {
  animation: segment-dotted 2s linear infinite;
  stroke-dasharray: 3, 5;
  stroke-width: 3px;
}

@keyframes segment-dotted {
  to {
    stroke-dashoffset: 24;
  }
}

/**
 * Daisy UI customizations
 */

.btn {
  @apply normal-case rounded-md;
}

.comp_summary h1,
.comp_summary h2,
.comp_summary h3 {
  @apply mb-4;
}

.disabled {
  opacity: 0.4;
  pointer-events: none;
}

.absolute-center {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@screen lg {
  .drawer .grid {
    grid-template-columns: max-content 1fr;
  }
}

.fade-in {
  transition: opacity 0.5s;
  opacity: 1 !important;
}

.react-photo-gallery--gallery > div {
  gap: 0.25rem;
}

.sticker {
  filter: drop-shadow(0.25rem 0.25rem 5px #fff)
    drop-shadow(-0.25rem 0.25rem 5px #fff)
    drop-shadow(0.25rem -0.25rem 5px #fff)
    drop-shadow(-0.25rem -0.25rem 5px #fff);
  transition: filter 0.3s ease-out;
}

.sticker:hover,
.sticker-select {
  filter: drop-shadow(0.25rem 0.25rem 1px #2962d9)
    drop-shadow(-0.25rem 0.25rem 1px #2962d9)
    drop-shadow(0.25rem -0.25rem 1px #2962d9)
    drop-shadow(-0.25rem -0.25rem 1px #2962d9);
}

/* keyframe animations */
.mask-path,
.reveal {
  opacity: 0;
  animation: reveal 0.4s ease-in forwards;
}

.slow-reveal {
  animation: reveal 1s ease-in;
}

.reveal-then-conceal {
  opacity: 0;
  animation: reveal-then-conceal 1.5s ease-in-out forwards;
}

@keyframes reveal {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes reveal-then-conceal {
  from {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

.background-animate {
  background-size: 400%;
  animation: pulse 3s ease infinite;
}

@keyframes pulse {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Fix for Safari and Mobile Safari:
Extracted Tailwind progress-bar styles and applied
them to a <div> instead of a <progress> element  */

.loading-bar {
  position: relative;
  width: 100%;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  overflow: hidden;
  height: 0.5rem;
  border-radius: 1rem;
  border-radius: var(--rounded-box, 1rem);
  vertical-align: baseline;
  background-color: hsl(var(--n) / var(--tw-bg-opacity));
  --tw-bg-opacity: 0.2;

  &::after {
    --tw-bg-opacity: 1;
    background-color: hsl(var(--n) / var(--tw-bg-opacity));
    content: '';
    position: absolute;
    top: 0px;
    bottom: 0px;
    left: -40%;
    width: 33.333333%;
    border-radius: 1rem;
    border-radius: var(--rounded-box, 1rem);
    animation: loading 5s infinite ease-in-out;
  }
}

@keyframes loading {
  50% {
    left: 107%;
  }
}

@keyframes inAnimation {
  0% {
    opacity: 0;
    max-height: 0px;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 1;
    max-height: 600px;
  }
}

@keyframes outAnimation {
  0% {
    opacity: 1;
    max-height: 600px;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 0;
    max-height: 0px;
  }
}

@keyframes ellipsisAnimation {
  0% {
    content: '';
  }
  25% {
    content: '.';
  }
  50% {
    content: '..';
  }
  75% {
    content: '...';
  }
}

.ellipsis::after {
  content: '';
  animation: ellipsisAnimation 1.5s infinite;
}
