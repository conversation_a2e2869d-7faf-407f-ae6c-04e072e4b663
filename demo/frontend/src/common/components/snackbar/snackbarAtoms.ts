/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import {atom} from 'jotai';

export type MessageType = 'info' | 'loading' | 'warning';

export type Message = {
  type: MessageType;
  text: string;
  duration: number;
  progress: number;
  startTime: number;
  expire: boolean;
  showClose: boolean;
  showReset: boolean;
};

export const messageAtom = atom<Message | null>(null);
