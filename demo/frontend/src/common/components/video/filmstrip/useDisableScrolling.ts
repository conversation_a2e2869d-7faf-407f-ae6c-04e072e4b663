/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import {useCallback, useEffect, useRef} from 'react';

function preventDefault(event: TouchEvent) {
  event.preventDefault();
}

export default function useDisableScrolling() {
  const isDisabledRef = useRef<boolean>(false);

  const disable = useCallback(() => {
    // Scrolling is already disabled
    if (isDisabledRef.current) {
      return;
    }
    isDisabledRef.current = true;
    document.body.addEventListener('touchmove', preventDefault, {
      passive: false,
    });
  }, []);

  const enable = useCallback(() => {
    // Scrolling is not disabled
    if (!isDisabledRef.current) {
      return;
    }
    isDisabledRef.current = false;
    document.body.removeEventListener('touchmove', preventDefault);
  }, []);

  useEffect(() => {
    // Enable scrolling again on unmount
    return () => {
      enable();
    };
  }, [enable]);

  return {
    disable,
    enable,
  };
}
