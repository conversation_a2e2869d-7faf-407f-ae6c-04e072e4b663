/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import {SAM2Model} from './SAM2Model';

export type Headers = {[name: string]: string};

export type TrackerOptions = {
  inferenceEndpoint: string;
};

export type Trackers = {
  'SAM 2': typeof SAM2Model;
};

export const TRACKER_MAPPING: Trackers = {
  'SAM 2': SAM2Model,
};
