/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import {Loading} from 'react-daisyui';

export default function LoadingMessage() {
  return (
    <div className="flex flex-col w-full h-full justify-center items-center bg-black text-white">
      <div className="flex justify-center">
        <Loading className="mr-2" /> Fetching data
      </div>
    </div>
  );
}
