# Copyright (c) Meta Platforms, Inc. and affiliates.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
The `ID` scalar type represents a unique identifier, often used to refetch an object or as key for a cache. The ID type appears in a JSON response as a String; however, it is not intended to be human-readable. When expected as an input type, any string (such as `"4"`) or integer (such as `4`) input value will be accepted as an ID.
"""
scalar GlobalID
  @specifiedBy(url: "https://relay.dev/graphql/objectidentification.htm")

type Mutation {
  createDeletionId: String!
  acceptTos: Boolean!
  acceptTermsOfService: String!
  uploadVideo(
    file: Upload!
    startTimeSec: Float = null
    durationTimeSec: Float = null
  ): Video!
  uploadSharedVideo(file: Upload!): SharedVideo!
  uploadAnnotations(file: Upload!): Boolean!
}

"""
An object with a Globally Unique ID
"""
interface Node {
  """
  The Globally Unique ID of this object
  """
  id: GlobalID!
}

"""
Information to aid in pagination.
"""
type PageInfo {
  """
  When paginating forwards, are there more items?
  """
  hasNextPage: Boolean!

  """
  When paginating backwards, are there more items?
  """
  hasPreviousPage: Boolean!

  """
  When paginating backwards, the cursor to continue.
  """
  startCursor: String

  """
  When paginating forwards, the cursor to continue.
  """
  endCursor: String
}

type Query {
  defaultVideo: Video!
  videos(
    """
    Returns the items in the list that come before the specified cursor.
    """
    before: String = null

    """
    Returns the items in the list that come after the specified cursor.
    """
    after: String = null

    """
    Returns the first n items from the list.
    """
    first: Int = null

    """
    Returns the items in the list that come after the specified cursor.
    """
    last: Int = null
  ): VideoConnection!
  sharedVideo(path: String!): SharedVideo!
}

type SharedVideo {
  path: String!
  url: String!
}

scalar Upload

type Video implements Node {
  """
  The Globally Unique ID of this object
  """
  id: GlobalID!
  path: String!
  posterPath: String
  width: Int!
  height: Int!
  url: String!
  posterUrl: String!
}

"""
A connection to a list of items.
"""
type VideoConnection {
  """
  Pagination data for this connection
  """
  pageInfo: PageInfo!

  """
  Contains the nodes in this connection
  """
  edges: [VideoEdge!]!
}

"""
An edge in a connection.
"""
type VideoEdge {
  """
  A cursor for use in pagination
  """
  cursor: String!

  """
  The item at the end of the edge
  """
  node: Video!
}
